<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design Better Solutions - We Buy Houses Fast</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .form-input {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-gray-800">Design Better Solutions</h1>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-blue-600 transition duration-300">Home</a>
                    <a href="#about" class="text-gray-700 hover:text-blue-600 transition duration-300">About</a>
                    <a href="#process" class="text-gray-700 hover:text-blue-600 transition duration-300">Process</a>
                    <a href="#contact" class="text-gray-700 hover:text-blue-600 transition duration-300">Contact</a>
                    <a href="#contact" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300">Get Cash Offer</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#home" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Home</a>
                <a href="#about" class="block px-3 py-2 text-gray-700 hover:text-blue-600">About</a>
                <a href="#process" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Process</a>
                <a href="#contact" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Contact</a>
                <a href="#contact" class="block mx-3 my-2 bg-blue-600 text-white px-4 py-2 rounded-lg text-center">Get Cash Offer</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="gradient-bg hero-pattern pt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                    We Buy Houses <span class="text-yellow-300">Fast</span>
                </h1>
                <p class="text-xl md:text-2xl text-white mb-8 max-w-3xl mx-auto">
                    Get a cash offer for your house in 24 hours. No repairs, no fees, no hassle. We pay more than anyone else!
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#contact" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-yellow-300 transition duration-300 shadow-lg">
                        Get My Cash Offer Now
                    </a>
                    <a href="#about" class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-gray-900 transition duration-300">
                        Learn More
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose Design Better Solutions?</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">We offer the fastest, most convenient way to sell your house with the highest cash offers in the market.</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center card-hover bg-gray-50 p-8 rounded-xl">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-dollar-sign text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Highest Cash Offers</h3>
                    <p class="text-gray-600">We pay more than anyone else in the market. Get the maximum value for your property with our competitive cash offers.</p>
                </div>
                <div class="text-center card-hover bg-gray-50 p-8 rounded-xl">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-clock text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">24-Hour Offers</h3>
                    <p class="text-gray-600">Receive your cash offer within 24 hours. No waiting, no uncertainty - just fast, reliable service.</p>
                </div>
                <div class="text-center card-hover bg-gray-50 p-8 rounded-xl">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-home text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Any Condition</h3>
                    <p class="text-gray-600">We buy houses in any condition. No repairs needed, no cleaning required - sell your house as-is.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">About Design Better Solutions</h2>
                    <p class="text-lg text-gray-600 mb-6">
                        We are a real estate investment company specializing in purchasing homes directly from homeowners. With years of experience in the industry, we've helped thousands of families sell their properties quickly and efficiently.
                    </p>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span class="text-gray-700">Cash purchases - no financing delays</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span class="text-gray-700">Subject-to and seller financing options</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span class="text-gray-700">No real estate agent commissions</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-3"></i>
                            <span class="text-gray-700">Close on your timeline</span>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Our Buying Methods</h3>
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-lg font-semibold text-blue-600 mb-2">Cash Purchases</h4>
                            <p class="text-gray-600">Quick, all-cash transactions with no financing contingencies.</p>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-blue-600 mb-2">Subject-To</h4>
                            <p class="text-gray-600">We take over your existing mortgage payments while you transfer the deed.</p>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-blue-600 mb-2">Seller Financing</h4>
                            <p class="text-gray-600">Flexible payment terms that work for your specific situation.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section id="process" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Simple 3-Step Process</h2>
                <p class="text-xl text-gray-600">Selling your house has never been easier</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center card-hover bg-gray-50 p-8 rounded-xl shadow-md">
                    <div class="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                        1
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Submit Your Property</h3>
                    <p class="text-gray-600">Fill out our simple form with your property details. It takes less than 2 minutes.</p>
                </div>
                <div class="text-center card-hover bg-gray-50 p-8 rounded-xl shadow-md">
                    <div class="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                        2
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Get Your Offer</h3>
                    <p class="text-gray-600">We'll analyze your property and present you with a competitive cash offer within 24 hours.</p>
                </div>
                <div class="text-center card-hover bg-gray-50 p-8 rounded-xl shadow-md">
                    <div class="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl font-bold">
                        3
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Close & Get Paid</h3>
                    <p class="text-gray-600">Choose your closing date and get paid. We handle all the paperwork and details.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
                <p class="text-xl text-gray-600">Real stories from homeowners who sold their properties to us</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 italic mb-6">"I needed to sell my house quickly due to a job relocation. Design Better Solutions offered me more than I expected and closed in just 10 days. The process was seamless!"</p>
                    <div class="flex items-center">
                        <div class="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-blue-600 font-bold">JD</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">John Davis</h4>
                            <p class="text-gray-500 text-sm">Atlanta, GA</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 italic mb-6">"My house needed major repairs that I couldn't afford. Design Better Solutions bought it as-is for a fair price. They were professional and honest throughout the entire process."</p>
                    <div class="flex items-center">
                        <div class="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-green-600 font-bold">SM</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Sarah Miller</h4>
                            <p class="text-gray-500 text-sm">Dallas, TX</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 flex">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-600 italic mb-6">"I was facing foreclosure and needed to sell quickly. Design Better Solutions offered me a subject-to deal that saved my credit and gave me the cash I needed. I'm so grateful!"</p>
                    <div class="flex items-center">
                        <div class="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                            <span class="text-purple-600 font-bold">RJ</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Robert Johnson</h4>
                            <p class="text-gray-500 text-sm">Phoenix, AZ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section id="contact" class="py-20 gradient-bg hero-pattern">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Get Your Cash Offer Today</h2>
                <p class="text-xl text-white">Fill out the form below and we'll contact you within 24 hours with a competitive offer</p>
            </div>
            <div class="bg-white rounded-xl shadow-2xl p-8">
                <form id="property-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                        <div>
                            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                    </div>
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Property Address *</label>
                        <input type="text" id="address" name="address" required class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" placeholder="123 Main St, City, State, ZIP">
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="bedrooms" class="block text-sm font-medium text-gray-700 mb-2">Bedrooms</label>
                            <select id="bedrooms" name="bedrooms" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                                <option value="">Select</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5+">5+</option>
                            </select>
                        </div>
                        <div>
                            <label for="bathrooms" class="block text-sm font-medium text-gray-700 mb-2">Bathrooms</label>
                            <select id="bathrooms" name="bathrooms" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                                <option value="">Select</option>
                                <option value="1">1</option>
                                <option value="1.5">1.5</option>
                                <option value="2">2</option>
                                <option value="2.5">2.5</option>
                                <option value="3">3</option>
                                <option value="3.5">3.5</option>
                                <option value="4+">4+</option>
                            </select>
                        </div>
                        <div>
                            <label for="sqft" class="block text-sm font-medium text-gray-700 mb-2">Square Feet</label>
                            <input type="number" id="sqft" name="sqft" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" placeholder="e.g. 1500">
                        </div>
                    </div>
                    <div>
                        <label for="condition" class="block text-sm font-medium text-gray-700 mb-2">Property Condition</label>
                        <select id="condition" name="condition" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                            <option value="">Select Condition</option>
                            <option value="excellent">Excellent - Move-in ready</option>
                            <option value="good">Good - Minor repairs needed</option>
                            <option value="fair">Fair - Some repairs needed</option>
                            <option value="poor">Poor - Major repairs needed</option>
                        </select>
                    </div>
                    <div>
                        <label for="timeline" class="block text-sm font-medium text-gray-700 mb-2">When do you need to sell?</label>
                        <select id="timeline" name="timeline" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                            <option value="">Select Timeline</option>
                            <option value="asap">ASAP</option>
                            <option value="30days">Within 30 days</option>
                            <option value="60days">Within 60 days</option>
                            <option value="90days">Within 90 days</option>
                            <option value="flexible">I'm flexible</option>
                        </select>
                    </div>
                    <div>
                        <label for="comments" class="block text-sm font-medium text-gray-700 mb-2">Additional Comments</label>
                        <textarea id="comments" name="comments" rows="4" class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" placeholder="Tell us more about your property or situation..."></textarea>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition duration-300 shadow-lg">
                            Get My Cash Offer Now
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-2xl font-bold mb-4">Design Better Solutions</h3>
                    <p class="text-gray-300 mb-4">We buy houses fast with cash. No repairs, no fees, no hassle. Get the highest offer for your property today.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white transition duration-300">
                            <i class="fab fa-facebook-f text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition duration-300">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition duration-300">
                            <i class="fab fa-linkedin-in text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-300 hover:text-white transition duration-300">Home</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-white transition duration-300">About Us</a></li>
                        <li><a href="#process" class="text-gray-300 hover:text-white transition duration-300">Our Process</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-white transition duration-300">Get Offer</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                    <div class="space-y-2">
                        <p class="text-gray-300"><i class="fas fa-phone mr-2"></i> (*************</p>
                        <p class="text-gray-300"><i class="fas fa-envelope mr-2"></i> <EMAIL></p>
                        <p class="text-gray-300"><i class="fas fa-map-marker-alt mr-2"></i> Your City, State</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">&copy; 2024 Design Better Solutions. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // Close mobile menu if open
                mobileMenu.classList.add('hidden');
            });
        });

        // Form submission
        document.getElementById('property-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Basic validation
            const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address'];
            let isValid = true;

            requiredFields.forEach(field => {
                const input = document.getElementById(field);
                if (!data[field] || data[field].trim() === '') {
                    input.classList.add('border-red-500');
                    isValid = false;
                } else {
                    input.classList.remove('border-red-500');
                }
            });

            if (!isValid) {
                alert('Please fill in all required fields.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                document.getElementById('email').classList.add('border-red-500');
                alert('Please enter a valid email address.');
                return;
            }

            // Phone validation (basic)
            const phoneRegex = /^[\d\s\-\(\)\+]+$/;
            if (!phoneRegex.test(data.phone)) {
                document.getElementById('phone').classList.add('border-red-500');
                alert('Please enter a valid phone number.');
                return;
            }

            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                alert('Thank you! We\'ve received your property information and will contact you within 24 hours with a cash offer.');
                this.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });

        // Add scroll effect to navbar
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('nav');
            if (window.scrollY > 100) {
                navbar.classList.add('shadow-xl');
            } else {
                navbar.classList.remove('shadow-xl');
            }
        });

        // Add animation on scroll for cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards
        document.querySelectorAll('.card-hover').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>